// Test registration with Hostinger email
require('dotenv').config();
const axios = require('axios');
const { sequelize } = require('./config/database');
const User = require('./models/User');

async function testHostingerEmail() {
  console.log('🧪 Testing Registration with Hostinger Email...\n');

  const testEmail = `test${Date.now()}@example.com`;
  const testPassword = 'Password123';

  try {
    // Connect to database
    await sequelize.authenticate();
    console.log('✅ Connected to database\n');

    // Test Registration
    console.log('📝 Testing Registration...');
    console.log('Test Email:', testEmail);
    console.log('Test Password:', testPassword);
    
    const registrationData = {
      email: testEmail,
      password: testPassword,
      role: 'freelancer',
      first_name: '<PERSON>',
      last_name: 'Doe'
    };

    console.log('📤 Sending registration request...');
    const regResponse = await axios.post('http://localhost:5001/api/auth/register', registrationData);
    
    console.log('✅ Registration successful!');
    console.log('Response:', JSON.stringify(regResponse.data, null, 2));
    
    if (regResponse.data.success) {
      console.log('User ID:', regResponse.data.data.user.id);
      console.log('Is Verified:', regResponse.data.data.user.is_verified);

      // Check database for verification token
      console.log('\n🔍 Checking database for verification token...');
      const user = await User.findOne({ where: { email: testEmail } });
      
      if (user && user.verification_token) {
        console.log('✅ Verification token found in database');
        console.log('Token:', user.verification_token.substring(0, 20) + '...');
        console.log('Expires:', user.verification_expires);
        
        console.log('\n📧 Email should have been sent to Hostinger SMTP!');
        console.log('✅ Check your actual email inbox for verification email');
        console.log('📬 Email sent from: <EMAIL>');
        
        // Create verification URL for manual testing
        const verificationUrl = `http://localhost:5001/api/auth/verify/${user.verification_token}`;
        console.log('\n🔗 Manual Verification URL:', verificationUrl);
        
        // Cleanup
        console.log('\n🧹 Cleaning up test user...');
        await user.destroy();
        console.log('✅ Test user deleted');
        
        console.log('\n🎉 Test completed successfully!');
        console.log('📧 Hostinger email configuration is working!');
        
      } else {
        console.log('❌ No verification token found in database');
      }
    } else {
      console.log('❌ Registration failed:', regResponse.data.message);
    }

  } catch (error) {
    console.error('\n❌ Test failed!');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Response:', JSON.stringify(error.response.data, null, 2));
    } else if (error.code) {
      console.error('Error Code:', error.code);
      console.error('Error Message:', error.message);
    } else {
      console.error('Error:', error.message);
    }
  } finally {
    await sequelize.close();
  }
}

testHostingerEmail();
